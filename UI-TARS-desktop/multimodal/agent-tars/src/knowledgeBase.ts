// ===== Types =====
export interface KnowledgeEntry {
  id: string;
  category: string;
  condition: string;
  solution: string;
}

export interface MessageHistoryEntry {
  role: string;
  content: string;
  toolCalls?: any[];
}

// ===== Special Knowledge Base for Loop Prevention =====
export const SPECIAL_KNOWLEDGE_BASE: KnowledgeEntry[] = [
  {
    id: 'spothero_calendar_left_arrow_click_loop',
    category: 'click_loop',
    condition:
      'Repeatedly clicking the left arrow of the calendar without reaching the target month',
    solution: `
  🚨 **EMERGENCY OVERRIDE - CLICK LOOP RESOLUTION** 🚨
  
  **Core Idea**: Before each click, read the currently displayed month/year, identify the NEXT AVALIABLE month/year(e.g. If current is July 2025, and you need June, you need June 2026 (next available June), NOT June 2025 (past June)),NOT THE PAST month/year(e.g. June 2025). Compute how many months you are away from your target, then pick the shortest direction.
  
  **Corrective Steps**:
  1. 🔍 **Read the calendar header** to see current month/year (e.g., "I see July 2025")
  2. Identify the target month/year (e.g., If current is July 2025, and you need June, you need June 2026 (next available June), NOT June 2025 (past June))
  3. 🧮 **Compute the month difference** to your goal (June 2025 or June 2026):
     \`\`\`
     delta = (targetYear - currentYear) * 12
           + (targetMonthIndex - currentMonthIndex)
     \`\`\`
  4. ↔️ **Choose direction**:
     - If \`delta > 0\`, click the **right arrow** (move forward in time).  
     - If \`delta < 0\`, click the **left arrow** (move backward).  
  5. 🔄 **Repeat steps 1-3** until \`delta == 0\` (you're on the target month).  
  6. ✅ **Select the day** (e.g. click “18”).
  
  **Why This Works**: Booking systems only show future dates, so you can't choose past dates.
  `,
  },
  {
    id: 'healthline_click_scroll_loop',
    category: 'click_loop',
    condition:
      'Repeatedly clicking buttons (next, submit, load more, etc.) on healthline or similar content sites without progress',
    solution: `
  🚨 **EMERGENCY OVERRIDE - CLICK SCROLL LOOP RESOLUTION** 🚨
  
  **Core Idea**: When repeatedly clicking buttons without progress on content sites like healthline, the target element might be out of viewport, obscured, or requires scrolling to activate. Always scroll down to reveal hidden content before attempting clicks.
  
  **Corrective Steps**:
  1. 🔍 **Check current viewport**: Verify if the clicked element is visible and clickable in the current view.
  2. ⬇️ **Scroll down first**: Use \`browser_scroll\` tool to scroll down the page to reveal more content and ensure the target element is fully visible.
  
  **Why This Works**: 
  - Scrolling ensures elements are in the viewport and triggers any scroll-based events.
  - Many buttons become active only after content is fully loaded through scrolling.
  `,
  },
  {
    id: 'general_click_loop',
    category: 'click_loop',
    condition: 'Repeatedly clicking any button or element without progress on general websites',
    solution: `
  🚨 **EMERGENCY OVERRIDE - GENERAL CLICK LOOP RESOLUTION** 🚨
  
  **Core Idea**: When repeatedly clicking any element without progress, systematically check viewport visibility, scroll to ensure accessibility, and verify element state before re-attempting the action.
  
  **Corrective Steps**:
  1. 🔍 **Assess current state**: Check if the element is visible, enabled, and clickable in the current viewport.
  2. ⬇️ **Scroll to element**: Use \`browser_scroll\` to scroll down and ensure the target element is fully visible.
  3. ⬆️ **Scroll up if needed**: Sometimes elements move up after content loads - scroll up to check.
  4. ✅ **Proceed with alternative**: If waiting continues to fail, inform user about the issue and suggest manual intervention.
  `,
  },
  {
    id: 'healthline_wait_loop',
    category: 'wait_loop',
    condition:
      'Repeatedly waiting for page load without scrolling to check if the button is out of the viewport',
    solution: `
  🚨 **EMERGENCY OVERRIDE - WAIT LOOP RESOLUTION** 🚨
  
  **Core Idea**: When the page repeatedly doing something without progress (e.g. repeated wait() actions), automatically scroll the page to ensure all elements are visible, and continue operations when content updates are detected.
  
  **Corrective Steps**:
  1. 🔍 **Wait for page load**: After an input or page update, first check if the page is fully loaded, ensuring all necessary elements are displayed.
  2. ⬇️ **Auto scroll**: use \`browser_scroll\` tool to scroll the page.
  3. ⏳ **Wait for page load**:
      - If no new content has been loaded, continue waiting and repeat step 2.
      - If new content has been loaded, proceed with subsequent actions.
  4. 🔄 **Continue with operations**: Once the page has fully loaded, proceed with the next steps (such as input, click, etc.).
  
  **Why This Works**:
  - Auto-scrolling ensures all page elements are loaded into the viewport, avoiding operation failures due to hidden elements.
  - By constantly checking for page changes, you ensure actions are performed when the page is in the correct state, avoiding redundant waiting or incorrect inputs.
  `,
  },
  {
    id: 'general_wait_loop',
    category: 'wait_loop',
    condition:
      'Repeatedly waiting for page changes or content loading without progress on general websites',
    solution: `
  🚨 **EMERGENCY OVERRIDE - GENERAL WAIT LOOP RESOLUTION** 🚨
  
  **Core Idea**: When repeatedly waiting for page changes or content loading without progress, systematically check viewport visibility, scroll to ensure accessibility, and verify element state before re-attempting the action.
  
  **Corrective Steps**:
  1. 🔍 **Assess waiting context**: Identify what you're waiting for (page load, content update, element appearance, etc.).
  2. ⬆️ **Scroll up to check**: Sometimes content appears above current viewport - scroll up to verify.
  3. 🎯 **Interact with page elements**: Click on visible buttons, links, or interactive elements that might trigger content loading.
  4. ⏳ **Set maximum wait time**: Don't wait indefinitely - if no progress after reasonable time, try alternative approaches.
  5. ✅ **Proceed with alternative**: If waiting continues to fail, inform user about the issue and suggest manual intervention.
  `,
  },
  {
    id: 'chase_drag_slider_loop',
    category: 'drag_loop',
    condition: 'Dragging a numeric slider with known target value but imprecise landing',
    solution: `
  🎯 **DRAG PRECISION PROTOCOL** - For numeric sliders with known target value
  
  **Step 1: Extract Current Value**
  - After each drag, read the displayed value from the slider (e.g., "Current age: 21")
  
  **Step 2: Build Mapping (for example, if the target value is 22, and the current value is 21, and the pixel position is 300, and the pixel position is 320, then the mapping is [300, 18], [320, 20])**
  - Record [pixel_position, value] pairs from history:
    - Drag 1: moved from pixel 300→320, value changed 18→20
    - Drag 2: moved from pixel 320→340, value changed 20→22
  - Compute pixels_per_unit = (320-300)/(20-18) = 10 pixels/year
  
  **Step 3: Calculate Target Offset**
  - If current value is 21 and target is 22:
    - Required value change = +1
    - Required pixel offset = 1 * 10 = 10 pixels
    - Drag from current position +10 pixels
  
  **Step 4: Validate**
  - After drag, extract new value. If not 22, repeat Step 3 with updated mapping.
  
  ⚠️ Always drag in the correct direction based on value comparison.
  `
  }
];
